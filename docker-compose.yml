
version: '3'

services:
  db:
    image: postgres
    # image: sqlite3
  web:
    build: .
    command: bash -c “python manage.py makemigrations && python manage.py migrate && python manage.py runserver localhost:8000”
    container_name: ecommerce-docker
    volumes:
      - .:/ecommerce-docker
    ports:
      - "8000:8000"
    depends_on:
      - db


# — — — — docker-compose.yml
# version: ‘3’
# services:
#  web:
#  build: .
#  command: bash -c “python manage.py makemigrations && python manage.py migrate && python manage.py runserver 0.0.0.0:8000”
#  container_name: <decommerce-container>
#  volumes:
#  — .:/<dir-in-container>
#  ports:
#  — “8000:8000”
 
 
#  — — — — requirements.txt
# Django==2.1.5
# gunicorn==19.9.0

# version: '3'

# services:
#   web:
#     restart: always
#     build: .
#     command: python manage.py runserver 0.0.0.0:8000
#     expose:
#       - "8000"
#     links:
#       - postgres:postgres
#       - redis:redis
#     volumes:
#       - web-django:/Users/<USER>/Desktop/API_ENV/ecommerce
#       # - web-static:/usr/src/app/static
#     env_file: .env
#     environment:
#       DEBUG: 'true'
    # command: /usr/local/bin/gunicorn ecommerce.wsgi:application -w 2 -b :8000

  # nginx:
  #   restart: always
  #   build: ./nginx/
  #   ports:
  #     - "80:80"
  #   volumes:
  #     - web-static:/www/static
  #   links:
  #     - web:web

  # postgres:
  #   restart: always
  #   image: postgres:latest
  #   ports:
  #     - "5432:5432"
  #   volumes:
  #     - pgdata:/var/lib/postgresql/data/

  redis:
    restart: always
    image: redis:latest
    ports:
      - "6379:6379"
    volumes:
      - redisdata:/data

volumes:
  web-django:
  web-static:
  pgdata:
  redisdata: