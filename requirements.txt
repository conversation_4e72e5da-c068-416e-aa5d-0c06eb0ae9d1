aioredis==2.0.1
alabaster==1.0.0
amqp==5.3.1
anyascii==0.3.2
anyio==4.6.2.post1
appdirs==1.4.4
argh==0.31.3
argon2-cffi==23.1.0
arrow==1.3.0
asgiref==3.8.1
asn1crypto==1.5.1
astroid==3.3.5
astropy==6.1.6
async-generator==1.10
async-timeout==5.0.1
atomicwrites==1.4.1
attrs==24.2.0
autobahn==24.4.2
Automat==24.8.1
autopep8==2.3.1
Babel==2.16.0
backcall==0.2.0
backports.functools-lru-cache==2.0.0
backports.shutil-get-terminal-size==1.0.0
backports.tempfile==1.0
backports.weakref==1.0.post1
bcrypt==4.2.0
beautifulsoup4==4.12.3
billiard==4.2.1
binaryornot==0.4.4
bitarray==3.0.0
bkcharts==0.2
black==24.10.0
bleach==6.2.0
bokeh==3.6.1
boto==2.49.0
Bottleneck==1.4.2
brotlipy==0.7.0
CacheControl==0.14.1
cached-property==2.0.1
cachetools==5.5.0
celery==5.4.0
certifi==2024.8.30
cffi==1.17.1
channels==4.2.0
channels-redis==4.2.1
chardet==5.2.0
charset-normalizer==3.4.0
click==8.1.7
click-didyoumean==0.3.1
click-plugins==1.1.1
click-repl==0.3.0
cloudpickle==3.1.0
# clyent==1.2.2
colorama==0.4.6
comtypes==1.4.8
constantly==23.10.4
construct==2.10.70
contextlib2==21.6.0
cookiecutter==2.6.0
cryptography==43.0.3
cycler==0.12.1
Cython==3.0.11
cytoolz==1.0.0
daal4py==2024.7.0
daphne==4.1.2
dask==2024.11.2
debugpy==1.8.8
decorator==5.1.1
defusedxml==0.7.1
diff-match-patch==20241021
distributed==2024.11.2
Django==5.1.3
django-allauth==65.2.0
django-core==1.4.1
django-countries==7.6.1
django-crispy-forms==2.3
django-crontab==0.7.1
django-debug-toolbar==4.4.6
django-elasticsearch-dsl==8.0
django-elasticsearch-dsl-drf==0.22.5
django-extensions==3.2.3
django-extra-fields==3.0.2
django-filter==24.3
django-haystack==3.3.0
django-jet==1.0.8
django-js-asset==2.2.0
django-markdown-deux==1.0.6
django-model-utils==5.0.0
django-modelcluster==6.3
django-mptt==0.16.0
django-nine==0.2.7
django-notifications-hq==1.8.3
django-pagedown==2.2.1
django-phonenumber-field==8.0.0
django-randompinfield==0.4.7
django-rest-auth==0.9.5
django-rest-knox==5.0.2
django-taggit==6.1.0
django-treebeard==4.7.1
djangorestframework==3.15.2
djangorestframework-jwt==1.11.0
docutils==0.21.2
draftjs-exporter==5.0.0
drf-haystack==1.9.1
elasticsearch==8.16.0
elasticsearch-dsl==8.16.0
entrypoints==0.4
et-xmlfile==2.0.0
fastcache==1.1.0
fcm-django==2.2.1
filelock==3.16.1
firebase-admin==6.6.0
flake8==7.1.1
Flask==3.1.0
fonttools==4.55.0
fsspec==2024.10.0
future==1.0.0
gevent==24.11.1
glob2==0.7
google-api-core==2.23.0
google-api-python-client==2.153.0
google-auth==2.36.0
google-auth-httplib2==0.2.0
google-cloud-core==2.4.1
google-cloud-firestore==2.19.0
google-cloud-storage==2.18.2
google-crc32c==1.6.0
google-resumable-media==2.7.2
googleapis-common-protos==1.66.0
googletrans==3.0.0
greenlet==3.1.1
grpcio==1.68.0
grpcio-status==1.68.0
h11==0.14.0
h2==4.1.0
h5py==3.12.1
haystack==0.42
HeapDict==1.0.1
hiredis==3.0.0
hpack==4.0.0
hstspreload==2024.11.1
html5lib==1.1
httpcore==1.0.7
httplib2==0.22.0
httpx==0.27.2
hyperframe==6.0.1
hyperlink==21.0.0
idna==3.10
imagecodecs==2024.9.22
imageio==2.36.0
imagesize==1.4.1
importlib-metadata==8.5.0
incremental==24.7.2
inflection==0.5.1
iniconfig==2.0.0
# install==1.3.5
intervaltree==3.1.0
ipykernel==6.29.5
ipython==8.29.0
ipython-genutils==0.2.0
ipywidgets==8.1.5
isort==5.13.2
itsdangerous==2.2.0
jdcal==1.4.1
jedi==0.19.2
Jinja2==3.1.4
jinja2-time==0.2.0
joblib==1.4.2
json5==0.9.28
jsonfield==3.1.0
jsonschema==4.23.0
jupyter==1.1.1
jupyter-client==8.6.3
jupyter-console==6.6.3
jupyter-core==5.7.2
jupyter-server==2.14.2
jupyterlab==4.3.1
jupyterlab-pygments==0.3.0
jupyterlab-server==2.27.3
jupyterlab-widgets==3.0.13
keyring==25.5.0
kiwisolver==1.4.7
kombu==5.4.2
l18n==2021.3
lazy-object-proxy==1.10.0
libarchive-c==5.1
llvmlite==0.43.0
locket==1.0.0
lxml==5.3.0
Markdown==3.7
markdown2==2.5.1
MarkupSafe==3.0.2
matplotlib==3.9.2
matplotlib-inline==0.1.7
mccabe==0.7.0
# menuinst==1.0.0
mistune==3.0.2
mkl-fft==1.3.11
mkl-random==1.2.8
mkl-service==2.4.2
mock==5.1.0
more-itertools==10.5.0
mpmath==1.3.0
msgpack==1.1.0
multipledispatch==1.0.0
munkres==1.1.4
mypy-extensions==1.0.0
mysqlclient==2.2.6
navigator-updater==0.2.1
nbclassic==1.1.0
nbclient==0.10.0
nbconvert==7.16.4
nbformat==5.10.4
nest-asyncio==1.6.0
networkx==3.4.2
nltk==3.9.1
nose==1.3.7
notebook==7.2.2
numba==0.60.0
numexpr==2.10.1
numpy==2.1.3
numpydoc==1.8.0
oauthlib==3.2.2
olefile==0.47
openpyxl==3.1.5
packaging==24.2
pandas==2.2.3
pandocfilters==1.5.1
paramiko==3.5.0
parso==0.8.4
partd==1.4.2
path==17.0.0
pathlib2==2.3.7.post1
pathspec==0.12.1
patsy==1.0.1
pefile==2024.8.26
pep8==1.7.1
pexpect==4.9.0
phonenumbers==8.13.50
pickleshare==0.7.5
Pillow==11.0.0
pkginfo==1.11.2
pluggy==1.5.0
ply==3.11
poyo==0.5.0
prometheus-client==0.21.0
prompt-toolkit==3.0.48
proto-plus==1.25.0
protobuf==5.28.3
proxycrawl==3.2.2
psutil==6.1.0
psycopg2==2.9.10
ptyprocess==0.7.0
py==1.11.0
pyasn1==0.6.1
pyasn1-modules==0.4.1
pycodestyle==2.12.1
pycosat==0.6.6
pycparser==2.22
pycryptodome==3.21.0
pycurl==7.45.3
pydocstyle==6.3.0
pyerfa==*******
pyflakes==3.2.0
Pygments==2.18.0
PyJWT==2.10.0
pylint==3.3.1
pyls-spyder==0.4.0
PyNaCl==1.5.0
pyodbc===4.0.0-unsupported
pyOpenSSL==24.2.1
pyparsing==3.2.0
pyreadline==2.1
pyrsistent==0.20.0
PySocks==1.7.1
pytest==8.3.3
python-dateutil==2.9.0.post0
python-decouple==3.8
python-lsp-black==2.0.0
python-lsp-jsonrpc==1.1.2
python-lsp-server==1.12.0
python-memcached==1.62
python-ptrace==0.9.9
python-slugify==8.0.4
python3-openid==3.2.0
pytz==2024.2
PyWavelets==1.7.0
pywin32==308
pywin32-ctypes==0.2.3
pywinpty==2.0.14
PyYAML==6.0.2
pyzmq==26.2.0
QDarkStyle==3.2.3
qstylizer==0.2.3
QtAwesome==1.3.1
qtconsole==5.6.1
QtPy==2.4.2
regex==2024.11.6
requests==2.32.3
requests-oauthlib==2.0.0
rfc3986==2.0.0
rope==1.13.0
rsa==4.9
Rtree==1.3.0
ruamel-yaml-conda==0.15.80
scikit-image==0.24.0
scikit-learn==1.5.2
scikit-learn-intelex==2025.0.0
scipy==1.14.1
seaborn==0.13.2
Send2Trash==1.8.3
serpy==0.3.1
service-identity==24.2.0
simplegeneric==0.8.1
singledispatch==4.1.0
sip==6.8.6
six==1.16.0
sniffio==1.3.1
snowballstemmer==2.2.0
sortedcollections==2.1.0
sortedcontainers==2.4.0
soupsieve==2.6
Sphinx==8.1.3
sphinxcontrib-applehelp==2.0.0
sphinxcontrib-devhelp==2.0.0
sphinxcontrib-htmlhelp==2.1.0
sphinxcontrib-jsmath==1.0.1
sphinxcontrib-qthelp==2.0.0
sphinxcontrib-serializinghtml==2.0.0
sphinxcontrib-websupport==2.0.0
spyder==6.0.2
spyder-kernels==3.0.1
SQLAlchemy==2.0.36
sqlparse==0.5.2
statsmodels==0.14.4
swapper==1.4.0
sympy==1.13.3
tables==3.10.1
tablib==3.7.0
TBB==2022.0.0
tblib==3.0.0
telepath==0.3.1
terminado==0.18.1
testpath==0.6.0
text-unidecode==1.3
textdistance==4.6.3
threadpoolctl==3.5.0
three-merge==0.1.1
tifffile==2024.9.20
tinycss==0.4
toml==0.10.2
toolz==1.0.0
tornado==6.4.1
tqdm==4.67.0
traitlets==5.14.3
twilio==9.3.7
Twisted==24.10.0
twisted-iocpsupport==1.0.4
txaio==23.1.1
typed-ast==1.5.5
typing-extensions==4.12.2
tzdata==2024.2
ujson==5.10.0
unicodecsv==0.14.1
Unidecode==1.3.8
uritemplate==4.1.1
urllib3==2.2.3
vine==5.1.0
wagtail==6.3
watchdog==6.0.0
wcwidth==0.2.13
webencodings==0.5.1
Werkzeug==3.1.3
whichcraft==0.6.1
widgetsnbextension==4.0.13
Willow==1.9.0
win-inet-pton==1.1.0
win-unicode-console==0.5
wincertstore==0.2
wrapt==1.16.0
xlrd==2.0.1
XlsxWriter==3.2.0
xlwings==0.33.3
xlwt==1.3.0
xmltodict==0.14.2
yapf==0.43.0
zict==3.0.0
zipp==3.21.0
zope.event==5.0
zope.interface==7.1.1
