# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-03-11 18:14+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 "
"&& n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#: cart/views.py:97
msgid "your item has been deleted."
msgstr ""

#: ecommerce/settings/base.py:254
msgid "Arabic"
msgstr ""

#: ecommerce/settings/base.py:255
msgid "English"
msgstr ""

#: notifications/views.py:44
msgid "this notification is deleted successfuly."
msgstr ""

#: products/views.py:76
msgid "you don't own product"
msgstr ""

#: templates/account/email/email_confirmation_message.txt:1
#, python-format
msgid ""
"Hello from %(site_name)s!\n"
"\n"
"You're receiving this e-mail because user %(user_display)s has given yours "
"as an e-mail address to connect their account.\n"
"\n"
"To confirm this is correct, go to http://localhost:2000/account-confirm-"
"email/%(key)s\n"
msgstr ""

#: templates/account/email/email_confirmation_message.txt:7
#, python-format
msgid ""
"Thank you from %(site_name)s!\n"
"%(site_domain)s"
msgstr ""

#: templates/account/email/email_confirmation_subject.txt:3
msgid "Please Confirm Your E-mail Address"
msgstr ""

#: templates/account/email/password_reset_key_message.txt:1
#, python-format
msgid ""
"Hello from %(site_name)s!\n"
"\n"
"You're receiving this e-mail because you or someone else has requested a "
"password for your user account.\n"
"It can be safely ignored if you did not request a password reset. Click the "
"link below to reset your password."
msgstr ""

#: templates/account/email/password_reset_key_message.txt:8
#, python-format
msgid "In case you forgot, your username is %(username)s."
msgstr ""

#: templates/account/email/password_reset_key_message.txt:10
#, python-format
msgid ""
"Thank you for using %(site_name)s!\n"
"%(site_domain)s"
msgstr ""

#: templates/account/email/password_reset_key_subject.txt:3
msgid "Password Reset E-mail"
msgstr ""

#: templates/account/messages/cannot_delete_primary_email.txt:2
#, python-format
msgid "You cannot remove your primary e-mail address (%(email)s)."
msgstr ""

#: templates/account/messages/email_confirmation_sent.txt:2
#, python-format
msgid "Confirmation e-mail sent to %(email)s."
msgstr ""

#: templates/account/messages/email_confirmed.txt:2
#, python-format
msgid "You have confirmed %(email)s."
msgstr ""

#: templates/account/messages/email_deleted.txt:2
#, python-format
msgid "Removed e-mail address %(email)s."
msgstr ""

#: templates/account/messages/logged_in.txt:4
#, python-format
msgid "Successfully signed in as %(name)s."
msgstr ""

#: templates/account/messages/logged_out.txt:2
msgid "You have signed out."
msgstr ""

#: templates/account/messages/password_changed.txt:2
msgid "Password successfully changed."
msgstr ""

#: templates/account/messages/password_set.txt:2
msgid "Password successfully set."
msgstr ""

#: templates/account/messages/primary_email_set.txt:2
msgid "Primary e-mail address set."
msgstr ""

#: templates/account/messages/unverified_primary_email.txt:2
msgid "Your primary e-mail address must be verified."
msgstr ""

#: user_profile/serializers.py:31
msgid "Must include \"email\" and \"password\"."
msgstr ""

#: user_profile/serializers.py:42
msgid ""
"Must include \"username or \"email\" or \"phone number\" and \"password\"."
msgstr ""

#: user_profile/serializers.py:55
msgid ""
"Must include either \"username\" or \"email\" or \"phone number\" and "
"\"password\"."
msgstr ""

#: user_profile/serializers.py:88
msgid "User account is inactive."
msgstr ""

#: user_profile/serializers.py:91
msgid "please check your username or email or phone number or password."
msgstr ""

#: user_profile/serializers.py:103
msgid "This account don't have E-mail address!, so that you can't login."
msgstr ""

#: user_profile/serializers.py:105
msgid "E-mail is not verified."
msgstr ""

#: user_profile/serializers.py:111
msgid "This account don't have Phone Number!"
msgstr ""

#: user_profile/serializers.py:113
msgid "Phone Number is not verified."
msgstr ""

#: user_profile/serializers.py:131
msgid "A user is already registered with this phone number."
msgstr ""

#: user_profile/views.py:241
msgid "Please enter a valid email."
msgstr ""

#: user_profile/views.py:244
msgid "Password reset e-mail has been sent."
msgstr ""

#: user_profile/views.py:261
msgid "Password has been reset with the new password."
msgstr ""

#: user_profile/views.py:276
msgid "Congratulations, password has been Changed."
msgstr ""

#: user_profile/views.py:291
msgid "ok"
msgstr ""
