; ==================================
;  celery worker supervisor example
; ==================================

; the name of your supervisord program
[program:{name}]

; Set full path to celery program if using virtualenv
command={env_dir}/bin/celery -A {tasks.celery/project-name} worker --loglevel=INFO

; The directory to your Django project
directory=/home/<USER>/videofy

; If supervisord is run as the root user, switch users to this UNIX user account
; before doing any processing.
user=root

; Supervisor will start as many instances of this program as named by numprocs
numprocs=1

; Put process stdout output in this file
stdout_logfile=/var/log/celery/celery_stdout.log

; Put process stderr output in this file
stderr_logfile=/var/log/celery/celery_stderr.log

; If true, this program will start automatically when supervisord is started
autostart=true

; May be one of false, unexpected, or true. If false, the process will never
; be autorestarted. If unexpected, the process will be restart when the program
; exits with an exit code that is not one of the exit codes associated with this
; process’ configuration (see exitcodes). If true, the process will be
; unconditionally restarted when it exits, without regard to its exit code.
autorestart=true

; The total number of seconds which the program needs to stay running after
; a startup to consider the start successful.
startsecs=10

; Need to wait for currently executing tasks to finish at shutdown.
; Increase this if you have very long running tasks.
stopwaitsecs = 600

; When resorting to send SIGKILL to the program to terminate it
; send SIGKILL to its whole process group instead,
; taking care of its children as well.
killasgroup=true

; if your broker is supervised, set its priority higher
; so it starts first
priority=998

