# Generated by Django 2.1.11 on 2020-09-01 09:38

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import user_profile.models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('user_profile', '0009_auto_20200702_1833'),
    ]

    operations = [
        migrations.CreateModel(
            name='NationalIDImage',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(blank=True, upload_to=user_profile.models.national_image_path)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='national_ids', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
